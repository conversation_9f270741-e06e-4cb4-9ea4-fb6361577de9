# تقرير معلومات النظام الأساسية
## موقع: http://credit.minia.edu.eg

### 📋 **معلومات النظام الأساسية:**

#### **1. معلومات الجامعة:**
```json
{
  "university": "Minia University",
  "backgroundImageUrl": "/static/images/miniaUniversity.JPG",
  "studentsUserNames": "",
  "studentsPasswords": ""
}
```

#### **2. معلومات الخادم:**
```
Server: nginx/1.18.0 (Ubuntu)
Content-Type: text/html; charset=UTF-8
Connection: keep-alive
Accept-Ranges: bytes
```

#### **3. نظام إدارة المحتوى:**
- **النظام**: Ibn Alhaitham e-SIS software
- **المطور**: intlaqcit (https://www.intlaqcit.com/)
- **النوع**: Student Information System

### 🔧 **التقنيات المستخدمة:**

#### **Frontend:**
- **HTML5** - هيكل الصفحات
- **CSS3** - التصميم والتنسيق
- **JavaScript/jQuery** - التفاعل والوظائف
- **Bootstrap** - إطار عمل CSS
- **jQuery UI** - واجهة المستخدم

#### **Backend:**
- **Python/Django** (محتمل بناءً على CSRF tokens)
- **nginx** - خادم الويب
- **Ubuntu** - نظام التشغيل

#### **قاعدة البيانات:**
- **SQL Database** (محتمل بناءً على هيكل الاستعلامات)

### 📁 **هيكل الملفات المكشوف:**

#### **ملفات JavaScript:**
```
/static/js/index.js          - منطق تسجيل الدخول
/static/js/general.js        - وظائف عامة
/static/js/captcha.js        - نظام الكابتشا
/static/js/sdmenu.js         - قوائم النظام
```

#### **ملفات CSS:**
```
/static/new-css/bootstrap.min.css
/static/new-css/login-new.css
/static/css/jquery.alerts.css
```

#### **الخطوط:**
```
/static/new-fonts/Droid Arabic Kufi Regular.ttf
/static/new-fonts/NanumGothic-Regular.ttf
```

#### **الصور:**
```
/static/images/miniaUniversity.JPG
/static/css/captcha/BarCode2.jpg
/static/images/ahliaLogo.PNG
```

### 🔌 **نقاط الاتصال (API Endpoints):**

#### **متاحة بدون تسجيل دخول:**
```
GET  /                       - الصفحة الرئيسية
POST /general                - معلومات عامة
POST /stuJCI                 - خدمات الطلاب (محدودة)
```

#### **محمية (تتطلب تسجيل دخول):**
```
POST /getJCI                 - الاستعلامات الرئيسية
POST /studentLogin           - تسجيل الدخول
GET  /studentLogOut          - تسجيل الخروج
```

### 🛡️ **آليات الحماية:**

#### **الموجودة:**
- **CSRF Protection** - حماية من Cross-Site Request Forgery
- **Session Management** - إدارة الجلسات
- **Input Validation** - التحقق من المدخلات (جزئي)
- **Access Control** - التحكم في الوصول (جزئي)

#### **المفقودة:**
- **Rate Limiting** - تحديد عدد الطلبات
- **IP Blocking** - حظر IP المشبوهة
- **Complete Input Sanitization** - تنظيف شامل للمدخلات
- **Error Message Filtering** - تصفية رسائل الخطأ

### ⚠️ **الثغرات المكتشفة:**

#### **1. Information Disclosure:**
- كشف معلومات النظام عبر `/general`
- كشف هيكل قاعدة البيانات من أسماء المعاملات
- كشف مسارات الملفات والمجلدات

#### **2. Potential SQL Injection:**
- timeout عند إدخال `'` في تسجيل الدخول
- عدم تنظيف كامل للمدخلات

#### **3. Enumeration Attacks:**
- إمكانية تجربة أسماء مستخدمين
- استجابات مختلفة تكشف معلومات

#### **4. Directory Traversal:**
- مجلد `/static/` محمي جزئياً
- إمكانية الوصول لملفات معينة

### 🎯 **معلومات إضافية محتملة:**

#### **يمكن استخراجها بتقنيات متقدمة:**
- قائمة المستخدمين
- هيكل قاعدة البيانات
- ملفات التكوين
- سجلات النظام
- معلومات الطلاب (في حالة وجود ثغرات)

### 📊 **تقييم مستوى الأمان:**

| الجانب | التقييم | الملاحظات |
|---------|----------|-----------|
| **Authentication** | متوسط ⚠️ | يحتاج تحسين |
| **Authorization** | ضعيف ❌ | ثغرات في التحكم |
| **Input Validation** | ضعيف ❌ | غير كامل |
| **Error Handling** | ضعيف ❌ | يكشف معلومات |
| **Session Management** | جيد ✅ | مطبق بشكل صحيح |

### 🔒 **التوصيات الأمنية:**

#### **عاجلة:**
1. **حماية نقطة `/general`** - تطبيق authentication
2. **تنظيف رسائل الخطأ** - عدم كشف تفاصيل النظام
3. **تطبيق Rate Limiting** - منع الهجمات الآلية
4. **SQL Injection Protection** - استخدام Prepared Statements

#### **متوسطة الأولوية:**
1. **Web Application Firewall (WAF)**
2. **Input Sanitization** شامل
3. **Logging and Monitoring** محسن
4. **Security Headers** إضافية

#### **طويلة المدى:**
1. **Security Audit** شامل
2. **Penetration Testing** دوري
3. **Security Training** للمطورين
4. **Incident Response Plan**

---

**⚠️ تحذير أخلاقي**: هذا التقرير لأغراض تعليمية وتحسين الأمان فقط. استخدام هذه المعلومات لأغراض ضارة غير قانوني وغير أخلاقي.

**📅 تاريخ التقرير**: 30 مايو 2025
**🔍 نوع الفحص**: Passive Information Gathering
**⏱️ مدة الفحص**: 30 دقيقة
